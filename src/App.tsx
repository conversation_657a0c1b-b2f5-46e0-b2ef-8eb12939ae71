import { useEffect, useState } from "react";
import "./App.css";
import { Flex, Image, Typography } from "antd";
import { last } from "es-toolkit";
import { orderBy } from "es-toolkit/compat";

function App() {
  const [images, setImages] = useState<string[]>([]);

  useEffect(() => {
    const imageModules = import.meta.glob<{ default: string }>(
      "./assets/puppeteer_images/132894336/*.png",
      { eager: true }
    );

    const imagePaths = Object.values(imageModules).map((mod) => mod.default);

    setImages(imagePaths);
  }, []);

  return (
    <Flex vertical gap={10} justify="center">
      {orderBy(images, [
        (a) =>
          +(
            last(a.split("/"))?.split("_")[1].split(".")[0].replace("p", "") ||
            ""
          ),
      ]).map((image, index) => (
        <Flex vertical gap={4} style={{ alignSelf: "stretch" }}>
          <Typography.Text>
            P{index} {image}
          </Typography.Text>

          <Image
            style={{ height: "100vh", width: "auto" }}
            src={image}
            alt={`image-${index}`}
            preview={false}
          />
        </Flex>
      ))}
    </Flex>
  );
}

export default App;
